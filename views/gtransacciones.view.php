<?php
#region region DOCS
/** @var Transaccion[] $transacciones */
/** @var Transaccion[] $transaccionesgrouped */
/** @var Transaccion[] $transacciones_preview */
/** @var Budget[] $budgets */
/** @var float $disponible */
#endregion docs
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Transacciones</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region region HEAD ?>
    <?php require_once __ROOT__ . '/views/head.view.php'; ?>
    <link href="<?php echo htmlspecialchars(RUTA) ?>resources/assets/plugins/nvd3/build/nv.d3.css" rel="stylesheet"/>
    <?php #endregion head ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <?php #region region FORM ?>
        <form action="gtransacciones" method="POST">
            <input type="hidden" id="selvalor_alloc" name="selvalor_alloc">
            <div class="col" style="display: none">
                <button type="submit" id="sub_ver_allocations" name="sub_ver_allocations" class="btn btn-success w-100">sub_ver_allocations</button>
            </div>

            <!-- BEGIN row -->
            <div class="row">
                <!-- BEGIN link -->
                <div class="col-md-4 col-xs-12">
                    <a href="itransaccion" class="btn btn-primary w-100">
                        Agregar transaccion
                    </a>
                </div>
                <!-- END link -->
                <!-- BEGIN link -->
                <div class="col-md-4 col-xs-12">
                    <a href="ltransacciones" class="btn btn-default w-100">
                        Ver listado
                    </a>
                </div>
                <!-- END link -->
	            <?php #region region LINK ir a deudas ?>
	            <div class="col-md-4 col-xs-12">
		            <a href="ldeudas" class="btn btn-md btn-default w-100">
			            Ir a deudas
		            </a>
	            </div>
	            <?php #endregion LINK ir a deudas ?>
            </div>
            <!-- END row -->
            <?php #region region PANEL grafica ?>
            <div class="panel panel-inverse mt-3">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        Grafica
                    </h4>
                </div>
                <!-- BEGIN panel-body -->
                <div class="p-1 table-nowrap" style="overflow: auto">
	                <?php #region region ARRAY canales ?>
	                <?php foreach ($budgets as $budget): ?>
		                <!-- BEGIN row -->
		                <div class="row mt-2">
			                <div class="col bar-canales">
				                <?php #region region BAR canal ?>
				                <div class="progress"
				                     data-bs-toggle="modal"
				                     data-bs-target="#mdl_editar_canal"
				                     data-id_canal="<?php echo limpiar_datos($budget->id_budget) ?>"
				                     data-nombre_canal="<?php echo limpiar_datos($budget->canal) ?>"
				                >
					                <div class="progress-bar bg-primary fs-10px fw-bold" style="width: <?php echo $budget->porcentaje; ?>%">
						                <span><?php echo $budget->canal; ?>: $<?php echo format_currency($budget->valor); ?></span>
					                </div>
				                </div>
				                <?php #endregion BAR canal ?>
			                </div>
		                </div>
		                <!-- END row -->
	                <?php endforeach; ?>
	                <?php #endregion ARRAY canales ?>
                    <?php #region region ARRAY ingresos egresos ?>
                    <?php foreach ($transaccionesgrouped as $transacciongrouped): ?>
                        <!-- BEGIN row -->
                        <div class="row mt-2">
                            <div class="col bar-canales">
                                <div class="progress">
                                    <div class="progress-bar <?php echo ($transacciongrouped->tipo == 'EGRESO') ? "bg-danger" : "bg-success"; ?> fs-10px fw-bold" style="width: <?php echo $transacciongrouped->porcentaje; ?>%">
                                        <span><?php echo $transacciongrouped->tipo; ?>: $<?php echo format_currency($transacciongrouped->valor); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- END row -->
                    <?php endforeach; ?>
                    <?php #endregion ARRAY ingresos egresos ?>
                    <!-- BEGIN row -->
                    <div class="row mt-3">
                        <?php #region region CHART categorias grafica ?>
                        <div id="nv-bar-chart" class="h-350px"></div>
                        <?php #endregion CHART categorias  ?>
                    </div>
                    <!-- END row -->
                </div>
                <!-- END panel-body -->
            </div>
            <?php #endregion panel grafica ?>
            <?php #region region PANEL transacciones recientes ?>
            <div class="panel panel-inverse mt-3">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        Transacciones Recientes
                    </h4>
                </div>
                <!-- BEGIN panel-body -->
                <div class="table-responsive">
                    <table class="table table-hover table-sm">
                        <thead>
                            <tr>
                                <th class="w-70px"></th>
                                <th class="text-center">Fecha</th>
                                <th class="text-center">Tipo</th>
                                <th class="text-center">Valor</th>
                                <th class="text-center">Categorías</th>
                                <th class="text-center">Nota</th>
                                <th class="text-center">Budget</th>
                            </tr>
                        </thead>
                        <tbody class="fs-12px">
                            <?php if (empty($transacciones_preview)): ?>
                                <tr>
                                    <td colspan="7" class="text-center">No hay transacciones recientes</td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($transacciones_preview as $transaccion): ?>
                                    <tr>
                                        <td class="align-middle text-center">
                                            <button type="button" class="btn btn-xs btn-outline-info" onclick="verAllocations('<?php echo limpiar_datos(abs($transaccion->valor)); ?>');" title="Ver desglose de allocations" <?php echo ($transaccion->tipo != 'INGRESO') ? 'disabled' : ''; ?>>
                                                <i class="fa fa-chart-pie"></i>
                                            </button>
                                        </td>

                                        <td class="align-middle text-center"><?php echo formatDateWithSpanishDay($transaccion->fecha); ?></td>
                                        <td class="align-middle text-center">
                                            <span class="badge <?php echo ($transaccion->tipo == 'EGRESO') ? "bg-danger" : "bg-success"; ?>">
                                                <?php echo $transaccion->tipo; ?>
                                            </span>
                                        </td>
                                        <td class="align-middle text-end <?php echo ($transaccion->valor < 0) ? "text-danger" : "text-success"; ?>">
                                            $<?php echo format_currency(abs($transaccion->valor)); ?>
                                        </td>
                                        <td class="align-middle"><?php echo $transaccion->categoriasstring; ?></td>
                                        <td class="align-middle"><?php echo $transaccion->nota; ?></td>
                                        <td class="align-middle text-center"><?php echo $transaccion->budget->canal; ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                <!-- END panel-body -->
                <div class="panel-footer text-end">
                    <a href="ltransacciones" class="btn btn-sm btn-primary">
                        Ver todas las transacciones
                    </a>
                </div>
            </div>
            <?php #endregion PANEL transacciones recientes ?>
	        <?php #region region MODAL mdl_editar_canal ?>
	        <div class="modal fade" id="mdl_editar_canal">
		        <div class="modal-dialog modal-lg modal-dialog-centered">
			        <div class="modal-content">
				        <input type="hidden" id="mdl_editar_canal_id_canal" name="mdl_editar_canal_id_canal">

				        <div class="modal-header">
					        <h4 class="modal-title">
						        Editar canal: <span id="mdl_editar_canal_nombre_canal"></span>
					        </h4>
					        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
				        </div>
				        <div class="modal-body">
					        <!-- BEGIN text -->
					        <div class="col-md-12 col-xs-12">
						        <div class="mb-3">
							        <label class="form-label">Valor:</label>
							        <input type="text" name="mdl_editar_canal_valor" id="mdl_editar_canal_valor" class="form-control"/>
						        </div>
					        </div>
					        <!-- END text -->
					        <?php #region region SUBMIT sub_sub_editar_canal ?>
					        <div class="col-md-12 col-xs-12">
						        <button type="submit" id="sub_editar_canal" name="sub_editar_canal" class="btn btn-md btn-success w-100">
							        Editar
						        </button>
					        </div>
					        <?php #endregion SUBMIT sub_sub_editar_canal ?>
					        <div class="col-md-12 col-xs-12 mt-3">
						        <a href="#" class="btn btn-md btn-default w-100" data-bs-dismiss="modal">
							        Cancelar
						        </a>
					        </div>
				        </div>
				        <div class="modal-footer">

				        </div>
			        </div>
		        </div>
	        </div>
	        <?php #endregion MODAL mdl_editar_canal ?>
        </form>
        <?php #endregion form ?>
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/js_core.view.php'; ?>

<?php #region region JS d3 simplebar chart ?>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/plugins/d3/d3.min.js"></script>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/plugins/nvd3/build/nv.d3.min.js"></script>

<script>
    var barChartData = [{
        key: 'Cumulative Return',
        values: [
            <?php foreach ($transacciones as $transaccion): ?>
            {'label': '<?php echo $transaccion->categoria; ?>', 'value': <?php echo $transaccion->valor; ?>, 'color': '#<?php echo $transaccion->colorchart; ?>'},
            <?php endforeach; ?>
        ]
    }];

    nv.addGraph(function () {
        var barChart = nv.models.discreteBarChart()
            .x(function (d) {
                return d.label
            })
            .y(function (d) {
                return d.value
            })
            .showValues(true)
            .duration(250);

        barChart.yAxis.axisLabel("Valores");
        barChart.xAxis.axisLabel('Categorias');

        d3.select('#nv-bar-chart').append('svg').datum(barChartData).call(barChart);
        nv.utils.windowResize(barChart.update);

        return barChart;
    });
</script>
<?php #endregion JS d3 simplebar chart ?>
<?php #region region JS MODAL mdl_editar_canal ?>
<script type="text/javascript">
    $('#mdl_editar_canal').on('shown.bs.modal', function (event) {
        const button                 = $(event.relatedTarget);
        const recipient_id_canal     = button.data('id_canal');
        const recipient_nombre_canal = button.data('nombre_canal');

        const mdl_editar_canal_id_canal     = document.getElementById('mdl_editar_canal_id_canal');
        const mdl_editar_canal_nombre_canal = document.getElementById('mdl_editar_canal_nombre_canal');
        const mdl_editar_canal_valor        = document.getElementById('mdl_editar_canal_valor');

        mdl_editar_canal_id_canal.value         = recipient_id_canal;
        mdl_editar_canal_nombre_canal.innerText = recipient_nombre_canal;
        mdl_editar_canal_valor.value            = '';
        mdl_editar_canal_valor.focus();
    })
</script>
<?php #endregion JS MODAL mdl_editar_canal ?>
<?php #region region JS press enter events ?>
<script>
    pressenterandclick('mdl_editar_canal_valor', 'sub_editar_canal');
</script>
    <script type="text/javascript">
        function verAllocations(valor) {
            const sel = document.getElementById('selvalor_alloc');
            sel.value = valor;
            document.getElementById('sub_ver_allocations').click();
        }
    </script>

<?php #endregion JS press enter events ?>
<?php #endregion js ?>

</body>
</html>
