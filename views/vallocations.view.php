<?php
#region DOCS
/** @var float $selected_valor */
/** @var App\classes\Allocation[] $allocations */
/** @var array<string, App\classes\AllocationItem[]> $itemsByAllocation */
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Desglose de Allocations</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region HEAD ?>
    <?php require_once __ROOT__ . '/views/head.view.php'; ?>
    <?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <!-- BEGIN page-header -->
        <div class="d-flex align-items-center justify-content-between">
            <h1 class="page-header mb-0">Desglose de Allocations</h1>
            <a href="gtransacciones" class="btn btn-default">Regresar</a>
        </div>
        <hr>
        <!-- END page-header -->

        <!-- Header summary -->
        <div class="row mb-3">
            <div class="col-md-12">
                <div class="alert alert-secondary mb-0">
                    <div>
                        <span class="badge bg-secondary fs-15px">Valor seleccionado: $<?php echo format_currency($selected_valor); ?></span>
                    </div>
                </div>
            </div>
        </div>

        <?php if (empty($allocations)): ?>
            <div class="alert alert-warning">No hay allocations activos.</div>
        <?php else: ?>
            <?php foreach ($allocations as $allocation): ?>
                <?php
                    $aNombre     = $allocation->getNombre();
                    $aPct        = (float)($allocation->getPorcentaje() ?? 0);
                    $aAmount     = (float)$selected_valor * ($aPct / 100.0);
                    $aId         = $allocation->getId();
                    $childItems  = $itemsByAllocation[$aId] ?? [];
                ?>
                <div class="panel panel-inverse mt-3">
                    <div class="panel-heading">
                        <h4 class="panel-title d-flex justify-content-between align-items-center">
                            <span><?php echo htmlspecialchars($aNombre); ?></span>
                            <span>
                                <span class="badge bg-primary ms-2 fs-12px"><?php echo number_format($aPct, 2); ?>%</span>
                                <span class="badge bg-success ms-2 fs-12px">$<?php echo format_currency($aAmount); ?></span>
                            </span>
                        </h4>
                    </div>
                    <div class="p-1 table-nowrap" style="overflow:auto">
                        <table class="table table-sm mb-0">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th class="text-center w-200px">%</th>
                                    <th class="text-end w-200px">Monto</th>
                                </tr>
                            </thead>
                            <tbody class="fs-13px">
                                <?php if (empty($childItems)): ?>
                                    <tr>
                                        <td colspan="3" class="text-center text-muted">Sin items</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($childItems as $item): ?>
                                        <?php
                                            $iNombre = $item->getNombre();
                                            $iPct    = (float)($item->getPorcentaje() ?? 0);
                                            $iAmount = $aAmount * ($iPct / 100.0);
                                        ?>
                                        <tr>
                                            <td class="align-middle"><?php echo htmlspecialchars($iNombre); ?></td>
                                            <td class="align-middle text-center"><?php echo number_format($iPct, 2); ?>%</td>
                                            <td class="align-middle text-end">$<?php echo format_currency($iAmount); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>

    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php require_once __ROOT__ . '/views/js_core.view.php'; ?>
</body>
</html>

