<?php 

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

global $conexion;

use App\classes\Allocation;
use App\classes\AllocationItem;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/general/preparar.php';

$newallocation = new Allocation;

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        if (isset($_GET['m'])) {
            $success_display = 'show';
            $success_text = 'La asignación ha sido modificada.';
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get

#region sub_add
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_add'])) {
    try {
        // Use setters and cast porcentaje to float
        $newallocation->setNombre(limpiar_datos($_POST['nombre']));
        $newallocation->setPorcentaje((float)limpiar_datos($_POST['porcentaje'])); // Cast to float

        // Use guardar method
        $newallocation->guardar($conexion);

        $success_display = 'show';
        $success_text = 'La asignación ha sido ingresada.';

        $newallocation = new Allocation();

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_add

#region sub_delallocation
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_delallocation'])) {
    try {
        $delidallocation = limpiar_datos($_POST['mdl_delallocation_idallocation']);

        Allocation::delete($delidallocation, $conexion);

        $success_display = 'show';
        $success_text = 'La asignación ha sido eliminada.';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_delallocation

#region set_edit_session
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'set_edit_session') {
    try {
        $_SESSION['allocation_id'] = limpiar_datos($_POST['allocation_id']);

        // Return success response for AJAX
        http_response_code(200);
        exit();

    } catch (Exception $e) {
        http_response_code(500);
        exit();
    }
}
#endregion set_edit_session

#region sub_editallocation
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_editallocation'])) {
    try {
        $_SESSION['allocation_id'] = limpiar_datos($_POST['selidallocation']);

        header('Location: editar-allocation');

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_editallocation

#region try
try {
    $allocations = Allocation::getList($conexion);

    // Prepare allocations array for view (keeping same structure for compatibility)
    $allocationsWithTotals = [];
    foreach ($allocations as $allocation) {
        $allocationsWithTotals[] = [
            'allocation' => $allocation
        ];
    }

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lallocations.view.php';

?>
