<?php 

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

global $conexion;

use App\classes\Allocation;
use App\classes\AllocationItem;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/general/preparar.php';
require_once __ROOT__ . '/src/classes/Allocation.php';
require_once __ROOT__ . '/src/classes/AllocationItem.php';

// Page variables
$selected_valor     = null;   // float amount selected from previous page (INGRESO)
$allocations        = [];     // Allocation[] active allocations
$itemsByAllocation  = [];     // map desordenado allocation id => AllocationItem[]

#region get
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    try {
        if (isset($_SESSION['allocations_valor'])) {
            // Clean and cast to float, allow decimals and thousand separators
            $valorRaw       = (string)$_SESSION['allocations_valor'];
            $valorLimpio    = function_exists('format_numberclean') ? format_numberclean($valorRaw) : $valorRaw;
            $selected_valor = (float)$valorLimpio;
        } else {
            // No session value, return to transacciones overview
            header('Location: gtransacciones');
            exit();
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text    = $e->getMessage();
    }
}
#endregion get

#region try
try {
    // Load active allocations and their items
    $allocations = Allocation::getList($conexion);
    foreach ($allocations as $allocation) {
        $aid = $allocation->getId(); // desordenado string id
        $itemsByAllocation[$aid] = AllocationItem::getByAllocation($aid, $conexion);
    }

} catch (Exception $e) {
    $error_display = 'show';
    $error_text    = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/vallocations.view.php';

?>

