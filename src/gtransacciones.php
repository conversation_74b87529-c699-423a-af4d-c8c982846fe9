<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/transaccion.php';
require_once __ROOT__ . '/src/classes/categoriatransaccion.php';
require_once __ROOT__ . '/src/classes/transaccioncategoria.php';
require_once __ROOT__ . '/src/classes/budget.php';
require_once __ROOT__ . '/src/general/preparar.php';

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
	try {
		if (isset($_GET['i'])) {
			$success_display = 'show';
			$success_text    = 'La transaccion ha sido ingresada.';
		}
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion get
#region sub_editar_canal
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_editar_canal'])) {
	try {
		$mod_budget            = new Budget();
		$mod_budget->id_budget = limpiar_datos($_POST['mdl_editar_canal_id_canal']);
		$mod_budget->valor     = limpiar_datos($_POST['mdl_editar_canal_valor']);
		$mod_budget->modify_valor($conexion);

		$success_display = 'show';
		$success_text    = 'El valor del canal ha sido modificado.';

	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_editar_canal
#region sub_ver_allocations
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_ver_allocations'])) {
	try {
		$valorRaw    = limpiar_datos($_POST['selvalor_alloc']);
		$valorLimpio = function_exists('format_numberclean') ? format_numberclean($valorRaw) : $valorRaw;
		$_SESSION['allocations_valor'] = $valorLimpio;

		header('Location: ver-allocations');
		exit();
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_ver_allocations

#region try
try {
	$transacciones         = Transaccion::getListGrouped($conexion);
	$transaccionesgrouped  = Transaccion::getListGroupedTipo($conexion);

	//preview de las ultimas transacciones ingresadas
	$param                 = [];
	$param['preview']      = 1;
	$transacciones_preview = Transaccion::getList($param, $conexion);

	$param               = array();
	$param['includetrk'] = 0;
	$budgets             = Budget::getList($param, $conexion);

} catch (Exception $e) {
	$error_display = 'show';
	$error_text    = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/gtransacciones.view.php';

?>